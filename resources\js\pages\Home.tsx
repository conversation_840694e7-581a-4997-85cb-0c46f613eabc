import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../components/ui/card';
import Navbar from '../components/Navbar';
import ProfilePhoto from '../components/ProfilePhoto';
import Bio from '../components/Bio';
import SkillSet from '../components/SkillSet';

const Home = () => (
  <div>
    <Navbar />
    <ProfilePhoto />
    <Bio />
    <SkillSet />
    <Card>
      <CardHeader>
        <CardTitle>Project</CardTitle>
      </CardHeader>
      <CardContent>
        <section id="project">Project</section>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Education</CardTitle>
      </CardHeader>
      <CardContent>
        <section id="education">Education</section>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Experience</CardTitle>
      </CardHeader>
      <CardContent>
        <section id="experience">Experience</section>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Organization</CardTitle>
      </CardHeader>
      <CardContent>
        <section id="organization">Organization</section>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <section id="activity">Activity</section>
      </CardContent>
    </Card>
  </div>
);

export default Home;
