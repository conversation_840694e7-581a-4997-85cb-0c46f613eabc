<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Home');
})->name('home');

Route::get('/articles', function () {
    return Inertia::render('Articles');
})->name('articles');

Route::get('/contact', function () {
    return Inertia::render('Contact');
})->name('contact');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    Route::get('/admin', function () {
        return Inertia::render('admin/AdminDashboard');
    })->name('admin');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
